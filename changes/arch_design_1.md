# 音频剧本生成优化架构设计（基于2M上下文与实际数据结构 - 分层处理版）

## 执行摘要

本文档基于对实际数据文件`save_witch_whole.json`的深度分析，并充分考虑利用**超长上下文能力（如Gemini 2.5 Pro 2M Token）及相关最佳实践**，提出了一个**以分层处理（概要生成后逐场景扩展）为核心、结构化数据驱动的音频剧本生成架构**。该架构首先利用LLM从完整的章节文本及结构化信息（`key_events`、`key_characters`等）中生成章节概要（场景列表及核心摘要），然后针对概要中的每个场景，在完整上下文（可缓存）的辅助下，聚焦生成详细的场景JSON。结合精准的Prompt工程（含指令末尾强调）、规则引擎验证、JSON Patch修复机制及Schema版本化，实现高质量音频剧本的高效、自动化生成，同时注重成本控制与系统韧性。

**核心原则：利用超长上下文进行全局理解，通过分层Prompt（概要-扩展）引导LLM专注处理；基于实际数据结构，通过JSON结构解析、LLM场景生成、规则引擎主导验证、LLM自我修复（Patch机制）及高级质量评估，最大限度地自动化高质量音频剧本的生成，同时严格控制成本、确保系统稳定性与可维护性。**

目标是不仅解决音频节目"谁在说话"、"情节是否吸引人"的核心挑战，更要确保系统在处理完整真实章节数据时的高效性、连贯性、可维护性和成本效益，并能优雅处理潜在的输入变更和性能瓶颈。

## 基于超长上下文最佳实践与实际数据的架构设计基础

本架构充分利用超长上下文能力、已发布的最佳实践和`save_witch_whole.json`的实际数据结构，针对以下关键点进行优化：
- **分层思考与专注生成**：避免直接让LLM在2M Token中漫无目的处理，先生成高级概要（场景ID、摘要），再逐场景聚焦扩展，提升输出质量和LLM专注度。
- **整体上下文与缓存利用**：保持完整章节信息在上下文中，并明确利用上下文缓存（`context_cache_id`）机制，大幅降低重复调用（如场景扩展、修复）的成本和延迟。
- **结构化输入深度利用**：充分利用`key_events`、`key_characters`、`narrative.themes`等结构化信息指导LLM的理解和生成。
- **角色关系与行为精准追踪**：在完整上下文中，LLM能更准确地理解和再现角色关系、称呼方式及行为发展。
- **场景信息直接映射**：利用`key_events`中的`location`和`scene_description`直接指导场景生成。
- **情节连贯性与吸引力提升**：全局视角有助于LLM把握整体剧情脉络，生成更连贯、更吸引人的情节。
- **指令精准传达**：通过在Prompt末尾重复关键指令，确保LLM遵循核心要求。
- **自动化与成本控制**：通过JSON Patch自我修复、缓存等机制减少人工干预和Token消耗。
- **系统韧性与可维护性**：引入Schema版本化和优雅降级策略。

## 核心理念：全局上下文准备 -> LLM章节概要生成 -> [LLM逐场景扩展 -> 规则验证 -> LLM自我修复(Patch)] -> 最终校验与高级评估 -> 输出

### 1. JSON结构解析与全局上下文准备 (JSON Structure Parsing & Global Context Preparation)
   - **目标**: (同前，但强调输出会包含`schema_version`)
   - **手段**: (同前，但增加) 将解析后的结构化信息与`narrative.content`组合，并**嵌入一个`schema_version`字段**（如 `"schema_version": "2025-06-05"`）。

### 2. LLM章节概要生成 (LLM Chapter Outline Generation) - 新阶段
   - **目标**: 利用LLM从全局上下文中提取并生成一个结构化的章节概要，包含所有计划生成的场景ID及其简短核心摘要或节拍。
   - **手段**: 
       - **输入**: 阶段1输出的全局上下文对象（含`schema_version`）。
       - **Prompt**: 指示LLM通读全部内容，识别关键事件和场景转折点（可参考`key_events`），输出一个JSON列表，每个对象包含`scene_id` (可基于`key_event`或顺序生成) 和`scene_summary` (一句话概括)。
       - **指令强调**: 在Prompt末尾重复关键指令，如"输出必须是一个JSON列表，每个元素包含scene_id和scene_summary"。
   - **输出**: 包含多个 `{scene_id, scene_summary}` 对象的JSON列表。

### 3. LLM逐场景扩展与JSON生成 (LLM Per-Scene Expansion & JSON Generation)
   - **目标**: 针对阶段2生成的概要中的每一个场景，引导LLM在完整的全局上下文（应被缓存）辅助下，聚焦生成详细的、符合预定义JSON Schema的音频剧本场景数据。
   - **手段**: 
       - **迭代处理**: 遍历阶段2输出的场景概要列表。
       - **输入/调用**: 对每个场景概要：
           - **主要Prompt**: 包含当前`scene_id`、`scene_summary`，并指示LLM专注于扩展此场景的细节。
           - **全局上下文**: 阶段1输出的完整全局上下文对象。**此部分应利用`context_cache_id`（基于章节内容哈希）通过Vertex AI API进行缓存**，以在处理同一章节的多个场景时及后续修复步骤中，避免重复计费和处理其静态内容。
       - **Prompt核心要素**: (类似前述版本阶段B的Prompt要素，但更强调聚焦于当前`scene_id`的扩展，同时利用可访问的全局上下文进行补充和确认)
           - 指示LLM参考全局`key_events`中与当前`scene_id`匹配的事件详情、全局`key_characters`中的角色信息等。
           - **指令强调**: 在Prompt末尾重复关键指令，如"输出必须是单个场景的JSON对象，严格符合SceneSchema"。
   - **输出**: 逐个生成符合JSON Schema的单个场景数据字符串。

### 4. 规则引擎验证与节奏控制 (Rule-Engine Validation & Pacing Control)
   - **目标**: (同前)
   - **手段**: (同前，主要针对单个生成的场景JSON进行处理)

### 5. LLM自我修复循环 (JSON Patch机制) (LLM Self-Repair Loop with JSON Patch)
   - **目标**: (同前，但强调使用JSON Patch)
   - **手段**: 
       - (错误定位与分类同前)
       - **差异化修复提示构建 (要求JSON Patch)**:
           - **输入**: 错误的场景JSON，相关的错误描述，以及完整的原始全局上下文（**应能通过`context_cache_id`复用**）。
           - **提示结构**: "...针对场景JSON: `{FAULTY_SCENE_JSON}`...发现问题: `{ERROR_DESCRIPTIONS}`...请严格参考完整的章节上下文和错误，**返回一个JSON Patch (RFC 6902)数组来修正这些错误**。例如：`[{\"op\": \"replace\", \"path\": \"/dialogue/2/speaker\", \"value\": \"罗兰\"}]`。"
           - **指令强调**: 在Prompt末尾重复关键指令，"返回必须是JSON Patch数组"。
       - **本地应用Patch**: LLM返回JSON Patch后，在本地将其应用到错误的场景JSON上。
       - (重新验证和重试控制同前)

### 6. 最终校验、高级评估与输出 (Final Validation, Advanced Assessment & Output)
   - **目标**: (同前，增加高级评估)
   - **手段**: 
       - (全局一致性、质量评估、打包等同前)
       - **新增：节奏曲线自动评估**: 
           - 对每个最终场景，可调用一个轻量级模型（如Gemini Flash，具有足够上下文窗口处理单个场景）或基于规则的分析器，为其主要内容（对话、旁白）生成一个或多个情感强度/节奏指标评分。
           - 将这些评分按场景顺序绘制成章节的"节奏/情感曲线"。
           - 自动标记出可能过于平淡、缺乏起伏的部分，供人工参考或进一步调整。

## 工作流程（基于超长上下文与实际数据优化版）

**整体流程图示概念:**
`全局上下文准备 (含Schema版本) -> LLM章节概要生成 -> [针对每个概要场景: (缓存的全局上下文) + LLM聚焦场景扩展 -> 规则验证 -> (缓存的全局上下文) + LLM自我修复(Patch)] -> (若通过) -> 最终校验与高级评估 -> 输出`

**阶段详述:**

- **阶段 A: JSON结构解析与全局上下文准备 (0次LLM调用)**
    - 输入: `save_witch_whole.json`文件中的单个章节数据。
    - 任务:
        1.  **结构化信息提取**: 直接解析JSON结构，提取以下信息：
            -   从`key_characters`提取角色信息：姓名、角色、行为、发展轨迹、角色间关系、已知称呼方式。
            -   从`key_events`提取事件信息：事件ID、事件描述、地点、场景描述、参与角色、大致顺序。
            -   从`narrative.themes`提取主题和情感基调。
            -   从`basic_info`提取章节基本信息。
        2.  **角色关系映射生成**: 基于`key_characters.interactions`和提取的角色信息，构建详细的角色关系和称呼映射表。
        3.  **全局上下文构建**: 将完整的章节`narrative.content`与所有提取的结构化信息（角色、事件、主题、关系映射等）组合成一个单一的、大型的上下文包，供后续LLM处理。此上下文旨在为LLM提供关于本章节的全部已知信息。
    - 输出: 单一的全局上下文对象/数据结构，包含章节文本、所有结构化元数据及`input_schema_version`。

- **阶段 B: LLM章节概要生成 (LLM Chapter Outline Generation) (1次LLM调用/章节)**
    - 输入: 阶段A输出的全局上下文对象。
    - 任务: LLM根据全局上下文，生成结构化的章节概要，包含场景ID列表及其核心摘要。
        - **Prompt核心要素**:
            -   明确指示LLM通读并理解全局上下文中的`narrative.content`、`key_events`和`key_characters`。
            -   要求LLM识别并列出本章节中所有构成独立场景的关键节点/事件/转折点。
            -   输出格式要求为一个JSON对象列表，每个对象代表一个待生成的场景，至少包含：
                -   `scene_id`: 一个唯一的场景标识符（例如，`ChapX_ScY`，或基于`key_event`的ID）。
                -   `scene_summary`: 对该场景核心内容、主要动作或对话焦点的一句话概括。
                -   （可选）`related_key_event_ids`: 与此场景直接相关的原始`key_event`的ID列表。
            -   **指令强调**: 在Prompt最末尾添加简洁的关键指令，例如："**重要输出指示**：请确保你的回复是一个JSON数组，其中每个元素都是一个包含`scene_id` (字符串)和`scene_summary` (字符串)键的JSON对象。不要包含任何markdown标记或解释性文本。"
    - 输出: 包含多个 `{scene_id, scene_summary}` 对象的JSON列表字符串。

- **阶段 C: LLM逐场景扩展与JSON生成 (N次LLM调用/章节, N为场景数)**
    - 输入: 
        - 阶段A输出的全局上下文对象 (期望通过`context_cache_id`实现缓存复用)。
        - 阶段B输出的场景概要列表中的单个 `{scene_id, scene_summary}` 对象。
    - 任务: 针对每个场景概要，LLM在**缓存的全局上下文**辅助下，聚焦扩展生成详细的单个场景JSON。
        - **Prompt核心要素**:
            -   提供当前要扩展的`scene_id`和`scene_summary`。
            -   明确指示LLM，当前任务是详细描述此单一场景，但必须严格参考和利用通过`context_cache_id`传入的完整章节全局上下文（包含完整的`narrative.content`、`key_events`、`key_characters`等）。
            -   强调LLM需根据全局信息确保角色行为、对话、称呼、地点、情节与整体故事线的一致性。
            -   要求输出严格符合预定义的`SceneSchema`的单个JSON对象。
            -   **指令强调**: 在Prompt最末尾添加简洁的关键指令，例如："**重要输出指示**：请确保你的回复是一个单个JSON对象，严格符合SceneSchema的规范。不要包含任何其他文字或解释。"
        ```json
        // SceneSchema 示例 (保持与之前一致，但强调其字段需与全局上下文对应)
        {
          "scene_id": "Evt1.Sc1", 
          "event_ids_covered": ["event_uuid_123"],
          "location": "城堡广场", 
          "beat_summary": "罗兰根据全局情节点推迟女巫处决的决定。",
          "dialogue": [
            {"speaker": "罗兰", "line": "今天的处决暂停...", "emotion": "坚定"},
            {"speaker": "民众", "line": "为什么？王子殿下！", "emotion": "愤怒"}
          ],
          "narration": ["冬日的寒风呼啸而过..."],
          "characters_present": ["罗兰", "巴罗夫", "民众"],
          "audio_cues": ["[人群喧哗声]", "[寒风声]"]
        }
        ```
    - 输出: 单个场景的JSON数据字符串。

- **阶段 D: 规则引擎验证与节奏控制 (0次API调用)**
    - 输入: 阶段C输出的单个JSON场景数据。
    - 任务: (基本同前，但更侧重对单个场景的校验，因为全局一致性更多依赖LLM在扩展阶段的全局视野)
        1.  **JSON解析与Schema验证**: (同前)
        2.  **角色一致性校验**: (同前，利用全局角色库和称呼映射表)
        3.  **场景信息一致性**: (同前)
        4.  **音频节奏与时长校验**: (同前)
        5.  **初步质量检查**: 检查是否有明显的内容缺失、逻辑不通等问题。
    - 输出: 验证通过的场景对象 + 详细的验证报告（针对此单个场景）。

- **阶段 E: LLM自我修复循环 (JSON Patch机制) (0-2次LLM调用/问题场景)**
    - 输入:
        - 阶段D产出的包含错误的场景JSON及其验证报告。
        - 阶段A输出的全局上下文对象 (期望通过`context_cache_id`实现缓存复用)。
    - 任务: LLM根据错误描述，在**缓存的全局上下文**辅助下，生成JSON Patch来修正错误。
        - **Prompt核心要素**:
            -   提供原始的（错误的）场景JSON。
            -   清晰列出规则验证发现的问题/错误描述。
            -   指示LLM参考完整的全局上下文（通过`context_cache_id`引用的、已缓存的全局上下文）来进行修正，确保修改与整体一致。
            -   严格要求LLM输出JSON Patch (RFC 6902)数组。
            -   **指令强调**: Prompt末尾强调："**重要输出指示**：你的回复必须是一个JSON Patch数组 (RFC 6902)。不要包含任何markdown标记或解释性文本。"
        - **本地应用Patch**: (同前)
    - 输出: 修正后的场景JSON数据 (若成功) 或 标记为"需人工审核"的场景。

- **阶段 F: 最终校验、高级评估与输出 (0次或少量轻量级LLM调用/章节)**
    - 输入: 所有通过阶段D/E验证的、代表整个章节的场景JSON对象列表。
    - 任务:
        1.  **章节级全局一致性检查**: (同前，现在有了阶段B的概要，可以比对场景列表是否完整覆盖了概要意图，以及所有重要`key_events`是否都有体现)。
        2.  **节奏曲线自动评估 (增强)**:
            -   **评分机制**: 针对每个通过的场景JSON，使用一个轻量级LLM（如Gemini Flash，同样可以利用缓存的全局上下文中的相关部分，或仅传入场景文本）或专门训练的分类器，从多个维度（如：情绪激烈度[1-5]，情节紧张度[1-5]，信息密度[1-5]）对场景的核心内容（特别是对话和关键旁白）进行打分。
            -   **曲线生成与可视化**: 将每个场景的各项评分按顺序绘制成多条曲线图，形成章节的"多维情感/节奏剖面图"。
            -   **模式识别与标记**: 自动识别并标记出可能的"平淡期"（连续低分）、"冗余高峰"（不必要的连续高分）或与预期`narrative.themes`不符的情感走向。
        3.  **整体质量与节奏评估**: (同前)
        4.  **剧本标准化与打包**: (同前)
        5.  **生成交付物**: (同前，质量报告中可包含节奏曲线图和分析)。
    - 输出: 最终的音频剧本文件 + 制作指导 + 质量报告（含节奏曲线图示）。

**总计API调用估算**:
-   **阶段A (上下文准备)**: 0次 LLM调用。
-   **阶段B (章节概要生成)**: 1次 LLM调用 / 章节。
-   **阶段C (逐场景扩展)**: N次 LLM调用 / 章节 (N为章节中的场景数量，一般章节可能包含5-15个场景)。单次调用Prompt包含当前场景概要和全局上下文引用（通过`context_cache_id`）。
-   **阶段E (自我修复)**: 平均0.2 - 0.5次 LLM调用 / 每个需要修复的问题场景。同样利用`context_cache_id`。
-   **阶段F (高级评估，可选)**: 若使用LLM进行节奏/情感评分，则 N次 轻量级LLM调用 / 章节。
-   **小计**: **一个典型章节（假设10个场景，20%需要修复）可能需要 1 (概要) + 10 (扩展) + 2*0.5 (修复) = 约12次核心LLM调用。** 每次扩展和修复调用都受益于上下文缓存。
-   **成本优化**: 
    -   **上下文缓存 (`context_cache_id`)** 是关键，它使得在阶段C的N次调用和阶段E的修复调用中，巨大的全局上下文（通常占Token的大头）不被重复计费和处理，仅对新增的、变化的Prompt部分（如当前场景概要、错误描述）和LLM的生成内容计费。
    -   JSON Patch修复机制减少了修复时LLM的输出Token量。
    -   总Token消耗 = 1次概要调用(全局上下文+概要输出) + N次扩展调用(小Prompt+场景JSON输出) + M次修复调用(小Prompt+Patch输出)。因缓存，实际计费Token远小于 N * (全局上下文Token)。

### 架构优化对比表
| 维度 | 上一版"基于实际数据的优化版（分块）" | 本版"基于2M上下文与实际数据结构" | 改进方向与理由 |
|------|------------------------------------|------------------------------------|----------------|
| **核心处理方式** | 基于`key_events`分块，逐块处理 | 章节级整体上下文，一次性或逐事件处理 | 利用超长上下文，提升连贯性与全局理解 |
| **文本输入给LLM** | `narrative.content`的片段 + 局部上下文 | 完整的`narrative.content` + 全局结构化信息 | 消除物理分块，LLM拥有最完整信息 |
| **上下文维护** | 块间连续性保证逻辑（如注入前块摘要） | 无需显式块间维护，LLM自带全局记忆 | 简化流程，降低上下文丢失风险 |
| **LLM场景生成** | 每块调用1次LLM生成场景 | 整个章节1次（批量）或N个`key_event`对应N次（逐事件）LLM调用 | 优化LLM调用模式，可能减少总调用次数 |
| **API调用估算/章节** | 约3-6次 (基于1.5次/块, 2-4块/章) | 约 N+1 次核心调用 (N为场景数)，但后续调用因缓存而Token成本较低 | 更可预测的调用模式，通过缓存有效控制总Token成本 |
| **角色与情节理解** | 依赖局部上下文和注入信息 | LLM基于完整章节信息进行判断 | 大幅提升角色行为和情节发展的全局一致性与合理性 |
| **实现复杂度** | 智能分块逻辑，上下文注入与同步 | 精准Prompt工程，处理超大输入输出 | 复杂度从数据预处理转向LLM交互与输出控制 |
| **潜在瓶颈** | 分块不当导致信息割裂 | 超大Prompt的构建、传输、LLM处理效率；超大JSON输出的解析 | 关注LLM本身对长上下文的处理性能 |
| **成本考量** | 多次小Token调用 | 可能次数更少但单次Token量巨大的调用 | 总Token成本需实测，但可能因调用次数减少而受益 |

## 基于2M上下文与实际数据的音频剧本生成架构设计

### A. JSON结构解析与全局上下文准备

#### 目标与价值
(同前，但强调输入`schema_version`的记录)

#### 技术实现
1.  **结构化数据深度解析**: (同前)
2.  **角色关系与称呼映射构建**: (同前)
3.  **全局上下文封装与版本化**:
    -   (同前) ...整合。
    -   在最终输出的全局上下文对象中，**嵌入一个`input_schema_version`字段**，记录所解析的 `save_witch_whole.json` 的Schema版本（例如，根据文件内元数据或配置文件确定）。这有助于后续的兼容性处理和问题追溯。

### B. LLM章节概要生成 (LLM Chapter Outline Generation)

#### 目标与价值
在不牺牲对整个章节完整理解的前提下，首先让LLM进行一次"粗加工"，提炼出章节的核心场景脉络。这既为后续的"精加工"提供了结构指引，也作为一种降低LLM在超长上下文中直接生成全部细节时可能出现的"失焦"或"遗忘"风险的手段。

#### 技术实现
1.  **输入**: 阶段A输出的包含`input_schema_version`的全局上下文对象。
2.  **Prompt工程**:
    -   指示LLM完整阅读并理解提供的全局上下文（包括`narrative.content`、`key_events`、`key_characters`等）。
    -   要求LLM基于其理解，识别出本章节中所有构成独立场景的关键节点/事件/转折点。
    -   输出格式要求为一个JSON对象列表，每个对象代表一个待生成的场景，至少包含：
        -   `scene_id`: 一个唯一的场景标识符（例如，`ChapX_ScY`，或基于`key_event`的ID）。
        -   `scene_summary`: 对该场景核心内容、主要动作或对话焦点的一句话概括。
        -   （可选）`related_key_event_ids`: 与此场景直接相关的原始`key_event`的ID列表。
3.  **指令强调**: 在Prompt的最后，用简洁明确的语言重申最重要的输出格式要求，例如："**重要输出指示**：请确保你的回复是一个JSON数组，其中每个元素都是一个包含`scene_id` (字符串)和`scene_summary` (字符串)键的JSON对象。不要包含任何markdown标记或解释性文本。"

### C. LLM逐场景扩展与JSON生成 (LLM Per-Scene Expansion & JSON Generation)

#### 目标与价值
针对概要中的每个场景"草稿"，利用LLM进行"精加工"。通过将LLM的注意力聚焦在单个场景的细节扩展上，同时让其能够随时查阅（通过缓存的）全局上下文以确保一致性和准确性，从而在控制推理复杂度的同时，生成高质量、符合整体设定的场景内容。

#### 技术实现
1.  **迭代处理**: 系统遍历阶段B生成的场景概要列表。
2.  **上下文缓存策略**: 对于当前章节的全局上下文对象（阶段A的输出），在首次用于本阶段或后续修复阶段时，通过Vertex AI API的`context_cache_id`参数（例如，使用章节内容的哈希值作为ID）将其提交并缓存。后续针对同一章节内其他场景的扩展调用，以及修复调用，都应使用相同的`context_cache_id`来复用已缓存的上下文，从而大幅减少计费Token和处理时间。
3.  **Prompt工程 (针对单个场景扩展)**:
    -   **输入**: 当前迭代到的`{scene_id, scene_summary, ?related_key_event_ids}`对象，以及通过`context_cache_id`引用的、已缓存的全局上下文。
    -   **核心指令**: 指示LLM当前任务是为给定的`scene_id`和`scene_summary`扩展生成完整的场景JSON。
    -   **聚焦与参考**: 强调LLM应主要依据`scene_summary`来构建场景核心，但必须不断参考完整的（缓存的）全局上下文中的相关信息（如`narrative.content`的对应段落、`key_events`中与`related_key_event_ids`相关的详细描述、所有`key_characters`的特征和关系、全局称呼映射等）来丰富细节、确保准确性和一致性。
    -   **输出要求**: 严格要求输出单个、符合预定义`SceneSchema`的JSON对象。
4.  **指令强调**: 同阶段B，在Prompt末尾重申关键的输出格式要求。

### D. 规则引擎验证与节奏控制

#### 目标与价值
(同前)

#### 技术实现
(基本同前，但现在明确是针对阶段C输出的单个场景JSON进行。部分全局一致性规则（如`key_event`是否都覆盖了）会移到最终校验阶段F。)
1.  **JSON Schema与格式校验**: (同前)
2.  **内容一致性校验 (参照全局上下文)**:
    -   **角色校验**: (同前，利用全局角色库和称呼映射表)
    -   **事件关联校验**: (同前，检查与`related_key_event_ids`的对应)
    -   **首次出场逻辑**: (同前)
3.  **音频节奏与时长校验模块**: (同前)
4.  **生成验证报告**: (同前，针对单个场景)

### E. LLM自我修复循环 (JSON Patch机制)

#### 目标与价值
(同前)

#### 技术实现
1.  **错误定位与分类**: (同前)
2.  **差异化修复提示构建 (要求JSON Patch)**:
    -   **输入**: 错误的场景JSON，错误描述，以及通过`context_cache_id`引用的、已缓存的全局上下文。
    -   **提示结构**: "重要：请参考通过context_cache_id提供的完整章节上下文。对于以下场景JSON：`{FAULTY_SCENE_JSON}`，规则检查发现这些问题：`{ERROR_DESCRIPTIONS}`。请生成一个JSON Patch (RFC 6902)数组来修正这些错误，确保修正后的内容与全局上下文保持一致。例如：`[{\"op\": \"replace\", \"path\": \"/dialogue/2/speaker\", \"value\": \"角色名\"}]`。"
3.  **指令强调**: Prompt末尾强调："**重要输出指示**：你的回复必须是一个JSON Patch数组 (RFC 6902)。不要包含任何markdown标记或解释性文本。"
4.  **本地应用Patch**: (同前)
5.  **LLM调用与重试**: (同前)

### F. 最终校验、高级评估与输出

#### 目标与价值
(同前)

#### 技术实现
1.  **章节级全局一致性检查**: (同前，现在有了阶段B的概要，可以比对场景列表是否完整覆盖了概要意图，以及所有重要`key_events`是否都有体现)。
2.  **节奏曲线自动评估 (增强)**:
    -   **评分机制**: 针对每个通过的场景JSON，使用一个轻量级LLM（如Gemini Flash，同样可以利用缓存的全局上下文中的相关部分，或仅传入场景文本）或专门训练的分类器，从多个维度（如：情绪激烈度[1-5]，情节紧张度[1-5]，信息密度[1-5]）对场景的核心内容（特别是对话和关键旁白）进行打分。
    -   **曲线生成与可视化**: 将每个场景的各项评分按顺序绘制成多条曲线图，形成章节的"多维情感/节奏剖面图"。
    -   **模式识别与标记**: 自动识别并标记出可能的"平淡期"（连续低分）、"冗余高峰"（不必要的连续高分）或与预期`narrative.themes`不符的情感走向。
3.  **整体质量与节奏评估**: (同前)
4.  **剧本标准化与打包**: (同前)
5.  **生成交付物**: (同前，质量报告中可包含节奏曲线图和分析)。

## 技术组件设计

### 核心组件
- **JSON数据解析与全局上下文构建器 (JSON Parser & Global Context Builder)**: 负责深度解析`save_witch_whole.json`（处理潜在的`input_schema_version`不匹配问题，并实现优雅降级），提取结构化信息，与`narrative.content`整合成包含`input_schema_version`的全局上下文对象。**管理全局上下文的哈希生成，用于`context_cache_id`。**
- **LLM章节概要协调器 (LLM Chapter Outline Coordinator)** - 新组件: 管理与LLM的交互，构建包含全局上下文和概要生成指令（含末尾指令强调）的Prompt，发送给LLM，接收并验证LLM生成的章节概要JSON列表。
- **LLM场景扩展协调器 (LLM Scene Expansion Coordinator)** - 新组件: 针对概要中的每个场景，管理与LLM的交互。构建聚焦于单场景扩展的Prompt（包含当前场景概要、全局上下文的`context_cache_id`引用、及末尾指令强调），发送给LLM，接收并验证生成的单个场景JSON。
- **规则验证引擎 (Rule Validation Engine)**: 对LLM生成的场景JSON进行全面的自动化校验，包括Schema符合性、与全局上下文（角色、事件、称呼等）的一致性、以及音频节奏指标的检查。
- **LLM自我修复管理器 (JSON Patch) (LLM Self-Repair Manager with JSON Patch)**: 根据规则报告，构建针对性的修复提示（包含错误场景、错误描述、全局上下文的`context_cache_id`引用、末尾指令强调要求JSON Patch），协调LLM进行场景修正，并**在本地应用返回的JSON Patch**。
- **最终校验与高级评估处理器 (Final Validation & Advanced Assessment Processor)** - 增强: 对整个章节场景列表进行最终全局一致性审查、质量评估（包括调用节奏曲线评估模块），并将合格场景打包输出。

### 辅助组件
- **全局角色知识库 (Global Character Knowledge Base)**: 由解析器构建和维护，包含所有角色的详细信息、关系和称呼映射，供LLM和验证引擎使用。
- **全局事件管理器 (Global Event Manager)**: 存储和管理从`key_events`提取的所有事件信息，辅助LLM进行场景定位和内容生成。
- **音频节奏分析器 (Audio Pacing Analyzer)**: 嵌入规则验证引擎，负责计算和评估场景的WPM、音效密度等节奏相关KPI。
- **节奏/情感曲线评估模块 (Pacing/Emotion Curve Assessment Module)** - 新组件: 使用轻量级LLM（如Gemini Flash）或规则分析器，为每个场景生成节奏/情感评分，并绘制章节曲线图。
- **Prompt模板库 (Prompt Template Library)**: 存储和管理用于不同生成阶段（初始场景生成、修复）和不同生成策略的Prompt模板。
- **上下文缓存管理器 (Context Cache Manager)**: 负责与Vertex AI API交互，确保在场景扩展和修复调用中正确使用和传递`context_cache_id`。
- **Schema版本与兼容性管理器 (Schema Version & Compatibility Manager)** - 新组件: 存储输入JSON的`schema_version`与内部处理逻辑的对应关系，当检测到`input_schema_version`不匹配时，尝试进行兼容性适配或触发优雅降级（如使用默认值填充缺失字段）。

## 成本控制与风险管理

### 成本控制策略
- **上下文缓存 (`context_cache_id`)**: **核心策略**。在逐场景扩展（阶段C）和自我修复（阶段E）时，通过`context_cache_id`复用首次调用时已处理的巨大全局上下文。这使得后续相关LLM调用的输入Token大幅减少（主要为当前场景概要、错误描述等变量部分），显著降低了总Token费用和API响应延迟。
- **分层Prompt与聚焦生成**: 先生成概要（1次调用），再N次聚焦场景扩展。虽然增加了调用次数，但每次扩展的推理更集中，可能提高一次通过率，减少修复次数。且扩展调用的计费Token因缓存而降低。
- **JSON Patch自我修复**: LLM仅返回差异化补丁，而非完整JSON，大幅减少修复阶段的输出Token量。
- **优化LLM交互次数**: 通过提升Prompt质量和分层处理，争取更高的首轮通过率，减少进入修复循环的场景比例。
- **LLM模型选择**: 主流程（概要生成、场景扩展、修复）可使用能力更强的模型（如Gemini Pro），节奏曲线评估等辅助任务可选用更轻量、成本更低的模型（如Gemini Flash）。
- **修复成本控制**: (同前，严格控制重试次数)。
- **监控Token消耗**: 实施详细的Token消耗监控，按阶段、按章节类型进行分析，持续优化。

### 风险管理机制
- **超长上下文处理的稳定性与效率**: (同前，但分层处理有助于降低单次调用的认知负荷)。
- **Prompt的鲁棒性**: (同前，增加末尾指令强调和分层设计来提升)。
- **输入数据结构变更 (`save_witch_whole.json`)**: 
    -   **Schema版本化**: 在全局上下文中嵌入`input_schema_version`。
    -   **兼容性映射**: 维护代码中的Schema版本兼容性逻辑。
    -   **优雅降级**: 若解析时发现关键字段缺失或结构不兼容（基于版本判断），系统应尝试使用预设的默认值或逻辑进行填充，并记录警告，而不是直接失败。目标是尽可能产出可用（即使可能不完美）的草稿。
- **错误累积与放大**: 分层处理中，概要质量直接影响后续场景扩展。需确保概要生成阶段的Prompt和验证足够鲁棒。单个场景扩展失败的影响被局限在单个场景。
- **API调用延迟与超时**: 
    -   单个LLM调用（特别是首次全局上下文处理，或无缓存的调用）可能耗时较长（如30-60秒）。
    -   **监控指标**: 针对各阶段（概要、扩展、修复）的P90/P95 API调用延迟进行监控。
    -   **超时熔断**: 为每次LLM调用设置合理的超时上限（例如Vertex AI建议的生产超时）。
    -   **重试与轮次上限**: 自我修复循环中，限制单个场景的修复轮次（如建议的2轮）。
- **降级策略 (Fallback)**: 
    -   **性能降级**: 如果P95的场景扩展时间（利用缓存后）持续超过阈值（如 > 90秒），或平均修复轮次 > 2，系统应发出警报，并可配置自动切换到更简单（但可能质量较低）的备用处理流程（如旧版的"分块处理"架构，或进一步简化Prompt要求）。
    -   **功能降级**: 若输入JSON的`schema_version`无法通过兼容性逻辑处理，可降级到仅尝试提取`narrative.content`进行更通用的文本到场景转换，并大量标记待人工审核。
- **可观测性**: 除了Token消耗和延迟，还需监控各阶段成功率、进入修复的场景比例、修复成功率等，为持续优化提供数据支持。

### 预算估算 (基于2M Token上下文，含缓存与分层处理)
- **主要成本**: LLM API调用费用。
- **核心思想**: 通过`context_cache_id`，章节的巨大全局上下文（假设占2M Token的90-95%）在概要生成后的所有相关调用中（N个场景扩展、M个修复）理论上不重复计费其静态部分。
- **调用构成与Token估算 (示例性)**:
    -   **概要生成 (1次/章)**: 输入 (完整全局上下文) + 输出 (概要JSON)。假设输入 1.8M Token，输出 0.01M Token。
    -   **场景扩展 (N次/章, N~10)**: 输入 (小Prompt + `context_cache_id`隐含的全局上下文) + 输出 (场景JSON)。实际计费输入Token可能仅为0.01M (小Prompt)，输出 0.02M (场景JSON)。总计 N * (0.01M + 0.02M)。
    -   **自我修复 (M次/问题场景, M<N)**: 输入 (小Prompt + `context_cache_id`) + 输出 (JSON Patch)。实际计费输入Token 0.005M，输出 0.001M。总计 M_total * (0.005M + 0.001M)。
- **对比无缓存**: 若无缓存，每次扩展和修复都需支付完整全局上下文的Token，成本将是N倍以上。
- **总Token成本**: 显著低于无缓存的"整体处理"模式，但仍需实际测试。主要受章节内场景数量和修复频率影响。
- **成本缓冲**: (同前，保持30-50%缓冲)。

## 实施优先级与路线图

### 第一阶段：核心引擎、分层处理与缓存基础（3-4周）
1.  **JSON解析与全局上下文构建器 (含版本化与缓存ID)**: 实现对`save_witch_whole.json`的解析，嵌入`input_schema_version`，生成`context_cache_id`。
2.  **LLM章节概要协调器**: 实现与LLM交互生成章节概要，包含末尾指令强调。
3.  **LLM场景扩展协调器 (与上下文缓存集成)**: 实现利用`context_cache_id`进行聚焦场景扩展，包含末尾指令强调。
4.  **规则验证引擎 (基础，针对单场景)**: (同前)。
5.  **LLM自我修复管理器 (JSON Patch与缓存集成)**: 实现基于JSON Patch的修复，利用`context_cache_id`，包含末尾指令强调。
6.  **端到端流程打通 (单章节概要->扩展->验证->修复)**: 能够跑通一个章节的分层处理流程。

### 第二阶段：鲁棒性、高级评估与监控（2-3周）
1.  **Schema版本与兼容性管理器**: 实现基本的输入Schema变更兼容处理和优雅降级逻辑。
2.  **节奏/情感曲线评估模块 (初步)**: 实现基于轻量级模型的场景评分和曲线生成。
3.  **最终校验与高级评估处理器 (集成曲线评估)**: (同前)。
4.  **监控与告警**: 建立核心指标（Token消耗、延迟、修复率、P95超时）的监控和告警机制。
5.  **超时与熔断机制 (初步)**: 实现基本的API调用超时和修复轮次上限控制。

### 第三阶段：系统优化与生产准备（2-3周）
1.  **成本与性能深度优化**: 根据监控数据，迭代优化Prompt、缓存策略、模型选择。
2.  **降级策略完善与测试**: 详细设计并测试在关键故障（如Schema不兼容、持续超时）下的降级路径。
3.  **错误处理与日志**: 完善错误处理和详细日志记录。
4.  **文档、全面测试与部署准备**: (同前)。

### 可选扩展功能（后续迭代）
-   更复杂的基于LLM的质量评估模块（如情节吸引力、角色弧线深度分析）。
-   针对特定角色实现更细致的声音风格指导（如通过少量黄金台词示例）。
-   多章节批量处理与管理界面。
-   与人工审核平台的集成。

## 结论

通过采纳**分层Prompt处理（概要生成后逐场景扩展）**、**上下文缓存 (`context_cache_id`)**、**JSON Patch修复**、**指令末尾强调**、**Schema版本化与优雅降级**以及**自动化节奏评估**等一系列行业最佳实践，本优化架构在充分利用超长上下文（如Gemini 2.5 Pro 2M Token）的基础上，显著提升了音频剧本生成系统的**效率、鲁棒性、可维护性和成本效益**。

### 核心优势 (对比上一版整体处理)
1.  **提升LLM专注度与输出质量**: 分层处理降低了单次LLM调用的认知负荷，使其能更好地聚焦于当前任务（概要提炼或场景细节扩展），从而可能产出更高质量、更符合预期的内容。
2.  **大幅优化成本与延迟**: 上下文缓存机制是关键，它使得构成Prompt主体的全局上下文信息在多次调用（场景扩展、修复）中不再重复计费和处理，显著降低了Token开销和API响应时间。
3.  **增强的系统韧性**: Schema版本化和优雅降级策略使得系统更能适应输入数据结构的潜在变化，减少了因上游变更导致的"硬失败"。
4.  **更高效的修复机制**: JSON Patch的应用使得修复过程更轻量，减少了数据传输和LLM输出的负担。
5.  **更精准的Prompt指令**: 在Prompt末尾重复关键指令，提高了LLM遵循核心要求的概率。
6.  **更全面的质量洞察**: 自动化的节奏/情感曲线评估为剧本质量提供了新的分析维度。

### 面临的挑战与展望
-   **对LLM能力的依赖**: (同前，但分层处理和缓存有助于更稳定地发挥其能力)。
-   **Prompt工程的持续优化**: 虽然引入了最佳实践，但针对概要、扩展、修复各阶段的Prompt仍需持续打磨和测试，以达到最佳效果。
-   **缓存策略与效果验证**: 实际缓存命中率和由此带来的成本节省效果需要在真实场景中充分验证。
-   **监控与降级阈值设定**: 合理的监控指标和降级触发阈值需要基于大量测试和实际运行数据来调整和优化。

此架构代表了在超长上下文时代，从"让LLM一次处理所有事"的朴素想法，向更精细化、更注重工程实践的"引导LLM分步专注处理并优化资源利用"的范式转变。它旨在构建一个既能发挥大模型强大能力，又能在实际生产环境中稳定、高效、经济运行的AI剧本生产线。这套结合了LLM能力与工程智慧的方案，将更有力地推动高质量音频内容的自动化创作。


